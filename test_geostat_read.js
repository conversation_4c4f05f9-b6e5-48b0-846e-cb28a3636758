const fs = require('fs');

// 简单测试读取 geoStat.log 文件
function testReadGeoStatLog() {
    try {
        const data = fs.readFileSync('/Users/<USER>/testCode/geoStat.log', 'utf8');
        console.log('文件大小:', data.length, '字符');
        console.log('文件开头:', data.substring(0, 200));
        console.log('文件结尾:', data.substring(data.length - 200));
        
        // 尝试解析
        if (data.trim().startsWith('[') && data.trim().endsWith(']')) {
            const result = new Function('return ' + data)();
            console.log('解析成功！数据条数:', result.length);
            console.log('第一条数据:', JSON.stringify(result[0], null, 2));
            console.log('最后一条数据:', JSON.stringify(result[result.length - 1], null, 2));
            return result;
        } else {
            throw new Error('文件格式不正确');
        }
    } catch (error) {
        console.error('读取失败:', error.message);
        return [];
    }
}

// 运行测试
console.log('开始测试读取 geoStat.log 文件...');
const data = testReadGeoStatLog();
console.log('测试完成');
